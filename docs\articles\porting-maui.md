# Porting Native to Drawn with DrawnUi

Why even bother?
* __You want complex layouts not to affect your app performance__  
_In some scenarios native layouts can be slower than drawn ones. Like 5 horses vs a car with a 5 horse power engine: for example, app has to handle 5 natives views instead of just 1 - the Canvas. Rasterized caching makes shadows and other heavy-duty elements never affect your performance._

* __Your designer gave you something to implement that pre-built controls can't handle__  
_DrawnUi is designed with freedom in mind, to be able to draw just about anything you can imagine. With direct access to canvas you can achieve exactly your unique result._

* __You want consistency across platforms__  
_On all platforms the rendering is done with same logic, make you certain that font, controls and layouts will render the same way._

* __You want to be in control__   
_DrawnUi is a lightweight open-source project that can be directly referenced and customized up to your app needs._

This guide will help you port your existing native controls to DrawnUi.

## Prerequisites

First please follow the Getting [Started guide](./getting-started.md) to setup your project for DrawnUi.

## The theory

To replace native controls with DrawnUi ones would take several steps: 

1. Create copies of your existing views.

2. Replace native views names with DrawnUi ones. 

3. Fix properties mismatch

4. Optimize: add caching etc

## Native vs Drawn names table

| Native MAUI Control | DrawnUi Equivalent | Notes |
|---------------------|-------------------|-------|
| **Layout Controls** |
| `Frame` | `SkiaFrame` | Alias for SkiaShape with Rectangle type |
| `VerticalStackLayout` | `SkiaStack` | Alias for SkiaLayout type Column with horizontal Fill |
| `HorizontalStackLayout` | `SkiaRow` | Alias for SkiaLayout type Row |
| `Grid` | `SkiaGrid` | Alias for SkiaLayout type Grid with horizontal Fill |
| `Grid` (single cell) | `SkiaLayer` | Alias for SkiaLayout type Absolute with horizontal Fill |
| `StackLayout` | `SkiaLayout` | Use Type="Column" or Type="Row" |
| `AbsoluteLayout` | `SkiaLayout` | Use Type="Absolute" |
| `FlexLayout` | `SkiaLayout` | Use Type="Wrap" |
| `ScrollView` | `SkiaScroll` | Scrolling container with virtualization |
| **Text Controls** |
| `Label` | `SkiaLabel` | High-performance text rendering with spans support |
| `Label` (with markdown) | `SkiaMarkdownLabel` | For complex formatting, emojis, multiple languages |
| **Input Controls** |
| `Entry` | `MauiEntry` | Native entry wrapped for DrawnUi |
| `Editor` | `MauiEditor` | Native editor wrapped for DrawnUi |
| **Button Controls** |
| `Button` | `SkiaButton` | Platform-specific styling via ControlStyle |
| **Toggle Controls** |
| `Switch` | `SkiaSwitch` | Platform-styled toggle switch |
| `CheckBox` | `SkiaCheckbox` | Platform-styled checkbox |
| `RadioButton` | `SkiaRadioButton` | Subclassed from SkiaToggle |
| **Image Controls** |
| `Image` | `SkiaImage` | High-performance image rendering |
| **Media Controls** |
| `Image` (media) | `SkiaMediaImage` | Subclassed SkiaImage for media |
| **Graphics Controls** |
| N/A | `SkiaSvg` | SVG rendering support |
| N/A | `SkiaGif` | Animated GIF support |
| N/A | `SkiaLottie` | Lottie animation support |
| **Navigation Controls** |
| `Shell` | `SkiaShell` | Navigation framework |
| `TabbedPage` | `SkiaViewSwitcher` | View switching functionality |
| **Specialized Controls** |
| N/A | `SkiaCarousel` | Swipeable carousel with snap points |
| N/A | `SkiaDrawer` | Swipe-in/out panel |
| N/A | `SkiaDecoratedGrid` | Grid with shape drawing between cells |


## The practice

Let's take a look at a simple example.  

