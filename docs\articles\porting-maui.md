# Porting Native to Drawn with DrawnUi

There can come a time when You feel that some complex parts of the app are not rendering the way You wish, or you cannot implement some UI with out-of-the box native controls. At the same time You want to stay with MAUI and not rewrite the app in something else.  
We can then replace chunks of our UI with drawn controls. Or for the whole app.

Why even bother?

* __You want complex layouts not to affect your app performance__  
_In some scenarios native layouts can be slower than drawn ones. Like 5 horses vs a car with a 5 horse power engine: for example, app has to handle 5 natives views instead of just 1 - the Canvas. Rasterized caching makes shadows and other heavy-duty elements never affect your performance._

* __Your designer gave you something to implement that pre-built controls can't handle__  
_DrawnUi is designed with freedom in mind, to be able to draw just about anything you can imagine. With direct access to canvas you can achieve exactly your unique result._

* __You want consistency across platforms__  
_On all platforms the rendering is done with same logic, make you certain that font, controls and layouts will render the same way._

* __You want to be in control__   
_DrawnUi is a lightweight open-source project that can be directly referenced and customized up to your app needs. When you meet a bug you can can hotfix it in the engine source code, and if you miss some property/control You can easily add them._

This guide will help you port your existing native controls to DrawnUi.

## Prerequisites

First please follow the Getting [Started guide](./getting-started.md) to setup your project for DrawnUi.

## The theory

To replace native controls with DrawnUi ones would take several steps: 

1. Create copies of your existing views.

2. Replace native views names with DrawnUi ones. 

3. Fix properties mismatch

4. Optimize: add caching etc

## Native vs Drawn names table

There are some direct alternatives to native controls You can use. At the same time now that you can "draw" you controls You can create your own controls from scratch.  
You can also just place MAUI controls over the canvas if You need to stick with native, use `SkiaMauiElement` as wrapper for them.

| Native MAUI Control | DrawnUi Equivalent | Notes |
|---------------------|-------------------|-------|
| **Layout Controls** |
| `Frame` | `SkiaFrame` | Alias for SkiaShape with Rectangle type |
| `VerticalStackLayout` | `SkiaStack` | Alias for SkiaLayout type Column with horizontal Fill |
| `HorizontalStackLayout` | `SkiaRow` | Alias for SkiaLayout type Row |
| `Grid` (single cell) | `SkiaLayer` | Alias for SkiaLayout type Absolute with horizontal Fill |
| `Grid` | `SkiaGrid` | Alias for SkiaLayout type Grid with horizontal Fill |
| N/A | `SkiaDecoratedGrid` | Grid with shape drawing between cells |
| `StackLayout` | `SkiaLayout` | Use Type="Column" or Type="Row" |
| `AbsoluteLayout` | `SkiaLayout` | Use Type="Absolute" |
| `FlexLayout` | `SkiaLayout` | Use Type="Wrap" |
| `ScrollView` | `SkiaScroll` | Scrolling container with virtualization |
| **Text Controls** |
| `Label` | `SkiaLabel` | High-performance text rendering with spans support |
| `Label` (with markdown) | `SkiaMarkdownLabel` | For complex formatting, emojis, multiple languages |
| **Input Controls** |
| `Entry` | `SkiaMauiEntry` | Native entry wrapped for DrawnUi |
| `Editor` | `SkiaMauiEditor` | Native editor wrapped for DrawnUi |
| **Button Controls** |
| `Button` | `SkiaButton` | Platform-specific styling via ControlStyle |
| **Toggle Controls** |
| `Switch` | `SkiaSwitch` | Platform-styled toggle switch |
| `CheckBox` | `SkiaCheckbox` | Platform-styled checkbox |
| `RadioButton` | `SkiaRadioButton` | Subclassed from SkiaToggle |
| **Image Controls** |
| `Image` | `SkiaImage` | High-performance image rendering |
| **Media Controls** |
| `Image` (media) | `SkiaMediaImage` | Subclassed SkiaImage for media |
| **Graphics Controls** |
| N/A | `SkiaSvg` | SVG rendering support |
| N/A | `SkiaGif` | Animated GIF support |
| N/A | `SkiaLottie` | Lottie animation support |
| **Shapes Controls** |
| `Frame` | `SkiaShape` | Container with border |
| `Border` | `SkiaShape` | Border decoration |
| `Ellipse` | `SkiaShape` | Ellipse shape |
| `Line` | `SkiaShape` | Line shape |
| `Path` | `SkiaShape` | Vector path shape |
| `Polygon` | `SkiaShape` | Polygon shape |
| `Polyline` | `SkiaShape` | Polyline shape |
| `Rectangle` | `SkiaShape` | Rectangle shape |
| `RoundRectangle` | `SkiaShape` | Rounded rectangle |
| **Navigation Controls** |
| `Shell` | `SkiaShell` | Navigation framework |
| `TabbedPage` | `SkiaViewSwitcher` | View switching functionality |
| **Specialized Controls** |
| `CollectionView` | `SkiaScroll`+`SkiaLayout` | Virtualized item collection |
| `ListView` | `SkiaScroll`+`SkiaLayout` | Simple item list |
| `CarouselView` | `SkiaCarousel` | Swipeable carousel with snap points |
| `SwipeView` | `SkiaDrawer` | Swipe actions on items |
| `RefreshView` | `LottieRefreshIndicator`/anything | Pull-to-refresh functionality |
| `ActivityIndicator` | `LottieRefreshIndicator`/anything | Loading/busy indicator |
| `Map` | `SkiaMapsUi` | Map control, SkiaMapsUi addon |
| N/A | `SkiaDrawer` | Swipe-in/out panel |
| N/A | `SkiaCamera` | Mlti-platform camera, SkiaCamera addon  |
| **Use native (wrap over canvas)** |
| `WebView` | `SkiaMauiElement`+`WebView` | wrap native over the canvas |
| `MediaElement` | `SkiaMauiElement`+`MediaElement` | Video/audio playback |
| `Picker` | `SkiaMauiElement`+`Picker` | Dropdown selection, create custom |
| `DatePicker` | `SkiaMauiElement`+`DatePicker` | Date selection control, create custom |
| `TimePicker` | `SkiaMauiElement`+`TimePicker` | Time selection control, create custom |
| `Slider` | `SkiaMauiElement`+`Slider`| Range input control, create custom |
| `Stepper` | `SkiaMauiElement`+`Slider` | Increment/decrement numeric input, create custom |
| `ProgressBar` | `SkiaMauiElement`+`Slider` | Progress indication, create custom |
| `TableView` | `SkiaMauiElement`+`TableView` | Grouped table display, create custom |
| `SearchBar` | ❌ Do not use, create custom | Search input with built-in styling |

## The practice

Let's take a look at a simple example.  

TODO

