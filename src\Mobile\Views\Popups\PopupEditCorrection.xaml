﻿<?xml version="1.0" encoding="utf-8"?>

<xam:PopupDialogBase
    x:Class="AppoMobi.Xam.PopupEditCorrection"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:AppoMobi.Forms.Controls"
    xmlns:controls1="clr-namespace:AppoMobi.Forms.UniversalEditor.Controls"
    xmlns:resX="clr-namespace:AppoMobi.Mobile.Import.Common.ResX"
    xmlns:svg="clr-namespace:AppoMobi.Forms.Controls.Svg"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    x:Name="ThisControl"
    HorizontalOptions="End"
    Color="Transparent">

    <!--<pages1:PopupPage.Animation>
        <animations1:MoveAnimation
            DurationIn="150"
            DurationOut="100"
            EasingIn="SinOut"
            EasingOut="SinIn"

            PositionIn="Right"
            PositionOut="Right" />
    </pages1:PopupPage.Animation>-->

    <!--  POPUP  -->
    <xam:GesturesGrid
        ColumnDefinitions="*,100,Auto"
        ColumnSpacing="0"
        RowDefinitions="*,Auto,80,*"
        RowSpacing="0"
        VerticalOptions="FillAndExpand">

        <xam:GesturesGrid
            Grid.Row="0"
            Grid.ColumnSpan="3"
            Tapped="Grid_OnDown"
            VerticalOptions="Fill" />

        <xam:GesturesGrid
            Grid.Row="2"
            Grid.ColumnSpan="3"
            Tapped="Grid_OnDown"
            VerticalOptions="Fill" />

        <xam:GesturesGrid
            Grid.Row="3"
            Grid.ColumnSpan="3"
            Tapped="Grid_OnDown"
            VerticalOptions="Fill" />

        <xam:GesturesGrid
            Grid.Row="1"
            Grid.Column="0"
            HorizontalOptions="Fill"
            Tapped="Grid_OnDown"
            VerticalOptions="Fill" />

        <xam:GesturesGrid
            Grid.Row="1"
            Grid.Column="1"
            HorizontalOptions="Fill"
            Tapped="Grid_OnDown"
            VerticalOptions="Fill" />

        <StackLayout
            Grid.Row="1"
            Grid.Column="2"
            BackgroundColor="{x:Static xam:BackColors.OptionLine}"
            HorizontalOptions="Start"
            Spacing="0"
            VerticalOptions="CenterAndExpand">

            <!--  HEADER  -->
            <xam:CGrid InputTransparent="True" VerticalOptions="StartAndExpand">

                <svg:GradientBox
                    EndColor="{x:Static xam:BackColors.GradientStartNav}"
                    GradientOrientation="Horizontal"
                    HorizontalOptions="Fill"
                    StartColor="{x:Static xam:BackColors.GradientEndNav}"
                    VerticalOptions="Fill" />

                <BoxView
                    BackgroundColor="#33000000"
                    HorizontalOptions="Fill"
                    VerticalOptions="Fill" />

                <!--  header text  -->
                <Label
                    x:Name="cTitle"
                    Margin="20,8,8,8"
                    HorizontalOptions="Fill"
                    Text="{Binding Title}"
                    TextColor="WhiteSmoke"
                    VerticalOptions="Fill"
                    VerticalTextAlignment="Center" />

            </xam:CGrid>


            <HorizontalStackLayout Margin="16,16" Spacing="10">

                <!-- MINUS -->
                <controls:TouchFrame
                Padding="1"
                WidthRequest="32"
                Stroke="{x:Static xam:TextColors.EntryDesc}"
                StrokeShape="RoundRectangle 8,8,8,8"
                TimeLockDownMs="1000"
                Up="OnTapped_MINUS">

                    <ContentView IsClippedToBounds="True">

                        <Grid
                        BackgroundColor="{x:Static xam:TextColors.EntryDesc}"
                        ColumnSpacing="1"
                        IsClippedToBounds="True"
                        VerticalOptions="StartAndExpand">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <!--  0  -->
                            <!--  back hotspot  -->
                            <xam:GesturesContentView
                            Grid.Column="0"
                            BackgroundColor="{x:Static xam:BackColors.Selected}"
                            HorizontalOptions="Fill"
                            VerticalOptions="Fill" />

                            <svg:GradientBox
                            Grid.Column="0"
                            EndColor="{x:Static xam:BackColors.GradientEndNav}"
                            GradientOrientation="Vertical"
                            InputTransparent="True"
                            Opacity="0.33"
                            StartColor="{x:Static xam:BackColors.GradientStartNav}"
                            VerticalOptions="FillAndExpand" />

                            <Label
                            Grid.Column="0"
                            Margin="6"
                            FontSize="14"
                            HorizontalOptions="Center"
                            InputTransparent="True"
                            LineBreakMode="TailTruncation"
                            Text="-"
                            TextColor="{x:Static xam:BackColors.Page}"
                            VerticalOptions="Center" />

                        </Grid>


                    </ContentView>


                </controls:TouchFrame>

                <Frame
                    MinimumWidthRequest="60"
                    VerticalOptions="Center"
                    Padding="1"
                    HasShadow="False">

                    <Label
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        Text="{Binding Source={x:Reference ThisControl}, Path=Data, StringFormat='{0:0.0} EV'}"
                        FontSize="14"
                        VerticalOptions="Center" />

                </Frame>

                <!-- PLUS -->
                <controls:TouchFrame
                Padding="1"
                WidthRequest="32"
                Stroke="{x:Static xam:TextColors.EntryDesc}"
                StrokeShape="RoundRectangle 8,8,8,8"
                TimeLockDownMs="1000"
                Up="OnTapped_PLUS">

                    <ContentView IsClippedToBounds="True">

                        <Grid
                        BackgroundColor="{x:Static xam:TextColors.EntryDesc}"
                        ColumnSpacing="1"
                        IsClippedToBounds="True"
                        VerticalOptions="StartAndExpand">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <!--  0  -->
                            <!--  back hotspot  -->
                            <xam:GesturesContentView
                            Grid.Column="0"
                            BackgroundColor="{x:Static xam:BackColors.Selected}"
                            HorizontalOptions="Fill"
                            VerticalOptions="Fill" />

                            <svg:GradientBox
                            Grid.Column="0"
                            EndColor="{x:Static xam:BackColors.GradientEndNav}"
                            GradientOrientation="Vertical"
                            InputTransparent="True"
                            Opacity="0.33"
                            StartColor="{x:Static xam:BackColors.GradientStartNav}"
                            VerticalOptions="FillAndExpand" />

                            <Label
                            Grid.Column="0"
                            Margin="6"
                            FontSize="14"
                            HorizontalOptions="Center"
                            InputTransparent="True"
                            LineBreakMode="TailTruncation"
                            Text="-"
                            TextColor="{x:Static xam:BackColors.Page}"
                            VerticalOptions="Center" />

                        </Grid>


                    </ContentView>


                </controls:TouchFrame>

            </HorizontalStackLayout>

            <!--  OK  -->
            <controls:TouchFrame
                x:Name="BtnOk"
                Margin="16,0,16,16"
                Padding="1"
                HorizontalOptions="Fill"
                Stroke="{x:Static xam:TextColors.EntryDesc}"
                StrokeShape="RoundRectangle 8,8,8,8"
                TimeLockDownMs="1000"
                Up="OnTapped_OK">


                <ContentView IsClippedToBounds="True">

                    <Grid
                        BackgroundColor="{x:Static xam:TextColors.EntryDesc}"
                        ColumnSpacing="1"
                        IsClippedToBounds="True"
                        VerticalOptions="StartAndExpand">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <!--  0  -->
                        <!--  back hotspot  -->
                        <xam:GesturesContentView
                            Grid.Column="0"
                            BackgroundColor="{x:Static xam:BackColors.Selected}"
                            HorizontalOptions="Fill"
                            VerticalOptions="Fill" />

                        <svg:GradientBox
                            Grid.Column="0"
                            EndColor="{x:Static xam:BackColors.GradientEndNav}"
                            GradientOrientation="Vertical"
                            InputTransparent="True"
                            Opacity="0.33"
                            StartColor="{x:Static xam:BackColors.GradientStartNav}"
                            VerticalOptions="FillAndExpand" />

                        <Label
                            Grid.Column="0"
                            Margin="6"
                            FontSize="14"
                            HorizontalOptions="Center"
                            InputTransparent="True"
                            LineBreakMode="TailTruncation"
                            Text="{x:Static resX:ResStrings.BtnOk}"
                            TextColor="{x:Static xam:BackColors.Page}"
                            VerticalOptions="Center" />


                        <!--<xam:FontIconLabel
                        Margin="0,0,8,0"
                        FontSize="14"
                        HorizontalOptions="End"
                        InputTransparent="True"
                        Opacity="0.5"
                        Text="{x:Static xam:FaPro.CheckCircle}"
                        TextColor="{x:Static xam:TextColors.Entry}"
                        VerticalOptions="Center" />-->

                    </Grid>


                </ContentView>


            </controls:TouchFrame>

        </StackLayout>


    </xam:GesturesGrid>


</xam:PopupDialogBase>