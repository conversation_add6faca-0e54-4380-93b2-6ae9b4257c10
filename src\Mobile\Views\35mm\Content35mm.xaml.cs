﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AppoMobi.Forms.Common.ResX;
using AppoMobi.Forms.Content.Camera.Extensions;


using AppoMobi.Models;
using AppoMobi.Pages;
using AppoMobi.Services;
using AppoMobi.UI;
using AppoMobi.Xam;
using AppoMobi.Touch;




namespace AppoMobi.Main
{

    public class Conversion : ObservableObject
    {
        public string Title { get; set; }
        public string Description { get; set; }
        public int Diagonal { get; set; }
        public double Koef { get; set; }
        private bool _Selected;
        public bool Selected
        {
            get { return _Selected; }
            set
            {
                if (_Selected != value)
                {
                    _Selected = value;
                    OnPropertyChanged();
                    OnPropertyChanged("SelectionColor");
                }
            }
        }


        public Color SelectionColor

        {
            get
            {
                if (Selected)
                {
                    return TextColors.PlaceholderActive;
                }
                return TextColors.EntryDesc;
            }
        }
    }

    public partial class Content35mm
    {
        //private MainVModel Model { get; set; }

        

        private bool lock_down { get; set; }
        
        public void AnimateIcon(View view)
        
        {
            if (lock_down) return;
            lock_down = true;

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                // Update the UI
                try
                {
                    await view.ScaleTo(1.25, 75);
                    await Task.Delay(50);
                    await view.ScaleTo(1.0, 75); lock_down = false;
                }
                catch (Exception e)
                {
                }
            });

        }


        
        public override  void OnRightIcon1Clicked()
        
        {
            Core.ShowHelp(ResStrings.X_35mmHelp);

            base.OnRightIcon1Clicked();
        }

 
        
        protected string FilterFocal(string input, int pos, int length)
        
        {
            //check numeric
            bool validated = input.All(Char.IsDigit);
            if (validated)
            {
                //check max value
                int inputValue = 0;
                if (pos > 0)
                {
                    inputValue = (EntryFocal.Text + input).ToInteger(); //mm
                }
                else
                {
                    inputValue = input.ToInteger(); //mm
                }
                if (inputValue < 0 || inputValue > 1500)
                    validated = false; //thou shell not pass
            }
            if (!validated) return "";
            return input;
        }

        public NiftyObservableCollection<Conversion> Items { get; } = new NiftyObservableCollection<Conversion>();

        
        public Content35mm(IPageEnhancedNav daddy) 
        
        {
            InitializeComponent();
            Init(daddy);

            Items.AddRange(new[]
            {
                new Conversion
                {
                    Title = "35mm",
                    Koef = 1.0,
                    Diagonal = 43
                },
                new Conversion
                {
                    Title = "6x4.5",
                    Koef = 1.6,
                    Diagonal = 70
                },
                new Conversion
                {
                    Title = "6x6",
                    Diagonal = 79,
                    Koef = 1.8
                },
                new Conversion
                {
                    Title = "6x7",
                    Diagonal = 88,
                    Koef = 2.0
                },
                new Conversion
                {
                    Title = "6x9",
                    Diagonal = 103,
                    Koef = 2.4
                },
                new Conversion
                {
                    Title = "6x12",
                    Diagonal = 132,
                    Koef = 3.1
                },
                new Conversion
                {
                    Title = "6x17",
                    Diagonal = 176,
                    Koef = 4.1
                },
                new Conversion
                {
                    Title = "7x17",
                    Diagonal = 467,
                    Koef = 10.9
                },
                new Conversion
                {
                    Title = "4x5''",
                    Diagonal = 153,
                    Koef = 3.8
                },
                new Conversion
                {
                    Title = "5x7''",
                    Diagonal = 209,
                    Koef = 4.9
                },
                new Conversion
                {
                    Title = "4x10''",
                    Diagonal = 265,
                    Koef = 6.2
                },
                new Conversion
                {
                    Title = "8x10''",
                    Diagonal = 315,
                    Koef = 7.3
                },
                new Conversion
                {
                    Title = "8x20''",
                    Diagonal = 537,
                    Koef = 12.5
                },
                new Conversion
                {
                    Title = "10x12''",
                    Diagonal = 395,
                    Koef = 9.2
                },
                new Conversion
                {
                    Title = "11x14''",
                    Koef = 10.4,
                    Diagonal = 450
                },
                new Conversion
                {
                    Title = "12x20''",
                    Diagonal = 590,
                    Koef = 13.7
                },
                new Conversion
                {
                    Title = "16x20''",
                    Diagonal = 632,
                    Koef = 14.7
                },
                new Conversion
                {
                    Title = "20x24''",
                    Diagonal = 793,
                    Koef = 18.4
                },
             });

            //prepare

            var index = 0;
            foreach (var item in Items)
            {
                if (Settings.Current.SelectedLang == "ru")
                {
                    item.Title.Replace(".", ",");
                }
                if (Settings.Current._35mmSelectedConversion == index)
                    SetSelection(item);
                index++;
            }

            DataGrid.ItemsSource = Items;

            if (Core.IsAndroid)
            {


            }
            else
            {


                if (Settings.Current.iModel == "iPhone X")//iphoneX
                {
                    cFstopStack.Spacing = 16;

                }
            }

            BindingContext = MainVModel.Instance;

            Daddy.RightIcon1Symbol.SetIcon(FontIcons.fa_info_circle);
            Daddy.ToggleButtonVisibility(ButtonType.Right1, true);


             cDesc1.FontSize = 24;
            cDesc1.TextColor = TextColors.PlaceholderActive.MultiplyAlpha(0.8f);

            cUnitsResult.FontSize = 24;
            cUnitsResult.TextColor = TextColors.PlaceholderActive.MultiplyAlpha(0.8f);

            if (Core.IsAndroid)
            {
                //cUnitsResult.
            }


            EntryFocal.Keyboard = Keyboard.Numeric;
            EntryFocal.HideNativePlaceholder = true;
            EntryFocal.NativePlaceholderText = "0";
            EntryFocal.PlaceholderText = ResStrings.X_FocalLength;

            EntryFocal.PlaceholderSmallTextSize = 12;


            EntryFocal.TextSize = 40;

            EntryFocal.prop1 = 40;

            //if (DeviceInfo.Current.Platform == DevicePlatform.Android)
            //{
            //    EntryFocal.AjustEntryHeight(EntryFocal.TextSize + 2 * 2);
            //    Entry_35mm.AjustEntryHeight(Entry_35mm.TextSize + 2 * 2);
            //}


            
            cHelp.Text = ResStrings.X_35mmDesc;

            EntryFocal.EditingEvent += OnChangedValued;
            EntryFocal.SetInputFilter(FilterFocal);

            //load settings
            lockUpdate = true;
            
            EntryFocal.Text = Settings.Current._35mmLastEntry;
            
            SetupUnits();

            lockUpdate = false;

         
            Recalculate();
        }

        protected override void OnDisposing()
        {
            EntryFocal.EditingEvent -= OnChangedValued;

            base.OnDisposing();
        }


        private bool lockUpdate = false;

        private void OnChangedValued(object sender, EventArgs eventArgs)
        {
            if (!lockUpdate)
                Recalculate();
        }

        List<string> unitsList = new List<string>
        {
            ResStrings.UnitsDescMm,
            ResStrings.UnitsDescInches
        };

        
        private async void UnitsFocalResult_OnTapped(object sender, DownUpEventArgs e)
        
        {
            if (Settings.Current._35mmFocalUnitsOutput == ResStrings.UnitsKeyMm)
            {
                Settings.Current._35mmFocalUnitsOutput = ResStrings.UnitsKeyInches;
            }
            else
            {
                Settings.Current._35mmFocalUnitsOutput = ResStrings.UnitsKeyMm;
            }

            SetupUnits();
            Recalculate();
            AnimateIcon(ContainerUnitsResult);
        }

        
        private async void UnitsFocal_OnTapped(object sender, DownUpEventArgs downUpEventArgs)
        
        {
            //var ret = await Core.Current.PresentSelectionList(Daddy, ResStrings.ChooseUnits, unitsList);
            //if (ret == null) return;

            if (Settings.Current._35mmFocalUnits == ResStrings.UnitsKeyMm)
            {
                Settings.Current._35mmFocalUnits = ResStrings.UnitsKeyInches;
            }
            else
            {
                Settings.Current._35mmFocalUnits = ResStrings.UnitsKeyMm;
            }
        
            SetupUnits();
            Recalculate();
            await Task.Delay(10);
            AnimateIcon(cDesc1);
        }

        
        protected void SetupUnits()
        
        {
            //focal
            if (Settings.Current._35mmFocalUnits == ResStrings.UnitsKeyMm)
            {
                cDesc1.Text = ResStrings.UnitsKeyMm; //todo can change to localized string other than Key
            }
            else if (Settings.Current._35mmFocalUnits == ResStrings.UnitsKeyInches)
            {
                cDesc1.Text = ResStrings.UnitsKeyInches;
            }


            if (Settings.Current._35mmFocalUnitsOutput == ResStrings.UnitsKeyMm)
            {
                cUnitsResult.Text = ResStrings.UnitsKeyMm; //todo can change to localized string other than Key
            }
            else if (Settings.Current._35mmFocalUnitsOutput == ResStrings.UnitsKeyInches)
            {
                cUnitsResult.Text = ResStrings.UnitsKeyInches;
            }

        }
        
        protected void Recalculate(Conversion item=null)
        
        {
            lockUpdate = true;

            try
            {
                if (item == null)
                {
                    item = Items.Selected;
                    if (item == null) throw new Exception("");
                }

                var index = Items.IndexOf(item);
                Settings.Current._35mmSelectedConversion = index;
                 Settings.Current._35mmLastEntry = EntryFocal.Text;

                var k = ((Conversion)item).Koef;
                //todo
                //focal
                double inputFocal = EntryFocal.Text.Replace(",", ".").ToDouble(); //mm default
                if (Settings.Current._35mmFocalUnits == ResStrings.UnitsKeyInches)
                {
                    //convert inches to mm
                    inputFocal = Calculate.InchesToMillimeters(inputFocal);
                }

                var result = Math.Round(inputFocal / k);

                var outText = $"{result}";
                if (Settings.Current._35mmFocalUnitsOutput == ResStrings.UnitsKeyInches)
                {
                    //convert mm to inches
                    result = result / 25.4;
                    outText = $"{result:0.##}";
                }

                cOutput.Text = outText;
                cHelp.Text = string.Format(ResStrings.X_35mmResultDesc, item.Koef, item.Diagonal, ResStrings.X_35mmDesc);//
            }
            catch (Exception exception)
            {
                cOutput.Text = $"0";
                cHelp.Text = ResStrings.X_35mmDesc;
            }

            lockUpdate = false;
        }

        protected bool OutputInches = false;

        
        private void SetSelection(Conversion selected)
        
        {
            foreach (var item in Items)
            {
                if (selected == item)
                {
                    item.Selected = true;
                    Items.Selected = item;
                }
                else
                    item.Selected = false;
            }
        }

        
        private void OnTapped_Item(object? sender, SkiaControl.ControlTappedEventArgs controlTappedEventArgs)
        {
            if (sender is BindableObject obj && obj.BindingContext is Conversion item)
            {
                SetSelection(item);
                Recalculate((Conversion)item);                    
            }
        }



    }

}
