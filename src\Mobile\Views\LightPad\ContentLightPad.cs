﻿using AppoMobi.Xam;

namespace AppoMobi.Main
{
    /// <summary>
    /// Temporary wrapper include drawn inside non-drawn
    /// </summary>
    public class ContentLightPad : ScreenCanvas
    {
        public ContentLightPad()
        {
 
        }

        public ContentLightPad(IPageEnhancedNav daddy)
        {
 

            Daddy = daddy;

            //TOOLBAR ICONS
            Daddy.RightIcon1Symbol.SetIcon(FontIcons.fa_info_circle);
            Daddy.ToggleButtonVisibility(ButtonType.Right1, true);

            //Daddy.RightIcon2Symbol.SetIcon(FontIcons.fa_cog);
            //Daddy.ToggleButtonVisibility(ButtonType.Right2, true);

        }

        public override void OnRightIcon1Clicked()
        {
            Core.ShowHelp(ResStrings.HelpLightPad);

            base.OnRightIcon1Clicked();
        }

        public override void OnRightIcon2Clicked()
        {
            //ShowOptions();

            base.OnRightIcon2Clicked();
        }

        protected override void Create(bool firsttime)
        {
            HorizontalOptions = LayoutOptions.Fill;
            VerticalOptions = LayoutOptions.Fill;

            //RenderingMode = RenderingModeType.Accelerated;

            Content = new LightPad() { };
        }

    }
}
