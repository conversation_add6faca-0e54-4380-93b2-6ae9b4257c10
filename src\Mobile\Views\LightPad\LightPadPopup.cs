﻿using System.Diagnostics;
using AppoMobi.Helpers;
using AppoMobi.Mobile.Views.Popups;
using DrawnUi.Models;

namespace AppoMobi.Main
{
    public class LightPadPopup : AppScreen
    {
        const float HIDE_TIMEOUT_SECS = 4.0f;
        const float ANIMATE_SPEED_SECS = 0.4f;
        private RestartingTimer _inactivityTimer;
        private readonly ScreenBrightnessController _controller;
        private SkiaSlider Slider;
        private SkiaLabel Label;
        private SkiaLayout MainLayout;
        private LightPadPopup _vm => this;
        private CancellationTokenSource cancellation = new();
        private double _Value;

        public override void OnDisposing()
        {
            base.OnDisposing();

            _inactivityTimer?.Stop();
            _inactivityTimer?.Dispose();
            _inactivityTimer = null;
        }

        public override void OnWillDisposeWithChildren()
        {
            _ = _controller.RestoreBrightnessAsync();

            Settings.Current.ScreenBrightness = Value;

            SetupScreenOn(false);

            base.OnWillDisposeWithChildren();
        }

        public void SetupScreenOn(bool start)
        {
            if (start)
            {
                Core.Native.ExecuteTask("cannotSleep");
            }
            else
            {
                if (Settings.Current.OptionScreenOn != "yes")
                {
                    Core.Native.ExecuteTask("canSleep");
                }
            }
        }

        public LightPadPopup()
        {
            _controller = new ScreenBrightnessController();

            _inactivityTimer = new RestartingTimer(TimeSpan.FromSeconds(HIDE_TIMEOUT_SECS), () => { HideControls(); });

            HorizontalOptions = LayoutOptions.Fill;
            VerticalOptions = LayoutOptions.Fill;

            BackgroundColor = Colors.White;

            Children = new List<SkiaControl>()
            {
                new SkiaLayout()
                    {
                        Children = new List<SkiaControl>()
                        {
                            new SkiaLabel()
                            {
                                MonoForDigits = "8",
                                UseCache = SkiaCacheType.Operations,
                                Margin = new(0, 0, 0, 40),
                                FontFamily = "FontText",
                                TextColor = Colors.Black,
                                FontSize = 80
                            }.Center()
                            .Assign(out Label),

                        //Ranged slider with Start End props
                        //using only End at this case
                        new SkiaSlider()
                            {
                                ControlStyle = PrebuiltControlStyle.Cupertino,
                                WidthRequest = 150,
                                HorizontalOptions = LayoutOptions.Center,
                                VerticalOptions = LayoutOptions.Center,
                                //TranslationY = 100,
                                TrackSelectedColor = AppColors.Accent,
                                //ThumbColor = AppColors.Primary,
                                Margin = new(0, 80, 0, 0)
                            }
                            .ObserveSelf((me, prop) =>
                            {
                                if (prop.IsEither(nameof(BindingContext), nameof(SkiaSlider.End)))
                                {
                                    _vm.Value = me.End; //context property set from control property  
                                }
                            })
                            .Observe(_vm, (me, prop) =>
                            {
                                if (prop.IsEither(nameof(BindingContext), nameof(Value)))
                                {
                                    me.End = _vm.Value; //control property set from observed context
                                }
                            })
                            .Assign(out Slider),
                        new AppButton(ResStrings.BtnClose)
                        {
                            UseCache = SkiaCacheType.Image,
                            HorizontalOptions = LayoutOptions.Center,
                            VerticalOptions = LayoutOptions.End,
                            Margin = 50
                        }.OnTapped((me) =>
                        {
                            Debug.WriteLine("CLOSE");
                            PopupPage.CloseAllPopups();
                        }),
                        new SkiaShape()
                        {
                            UseCache = SkiaCacheType.Operations,
                            Type = ShapeType.Circle,
                            TranslationY = -100,
                            TranslationX = 100,
                            //HorizontalPositionOffsetRatio = 1.5,
                            //VerticalPositionOffsetRatio = -1.5,
                            WidthRequest = 200,
                            StrokeWidth = 2,
                            StrokePath = new[] { 8.0, 8.0 },
                            StrokeColor = Colors.DarkGray,
                            VerticalOptions = LayoutOptions.Start,
                            HorizontalOptions = LayoutOptions.End,
                            LockRatio = 1,
                        }
                        }
                    }
                    .Assign(out MainLayout).Fill(),
                    
                new SkiaHotspot()
                {
                    VerticalOptions = LayoutOptions.Start,
                    HorizontalOptions = LayoutOptions.End,
                    LockRatio = 1,
                    WidthRequest = 100,
                    //BackgroundColor = Color.Parse("#55ff0000") //for debugging
                }.OnTapped((me) =>
                {
                    if (!MainLayout.IsVisible)
                    {
                        ShowControls();
                    }
                    else
                    {
                        HideControls();
                    }
                })
            };

            var was = Value;
            Value = Settings.Current.ScreenBrightness;
            if (was == Value)
            {
                OnPropertyChanged(nameof(Value));
            }

            SetupScreenOn(true);
        }

        public double Value
        {
            get { return _Value; }
            set
            {
                if (_Value != value)
                {
                    _Value = value;
                    OnPropertyChanged();

                    Debug.WriteLine($"Slider value: {value:0.00}");
                    _controller.SetBrightnessAsync((float)(value / 100.0));
                    Label.Text = $"{value:0}";
                    ReportWasTouched();
                }
            }
        }

        public void HideControls()
        {
            Debug.WriteLine("HIDE VIEW");

            _inactivityTimer.Stop();

            var oldCts = Interlocked.Exchange(ref this.cancellation, new CancellationTokenSource());
            if (oldCts != null && !oldCts.IsCancellationRequested)
            {
                oldCts.Cancel();
                oldCts.Dispose();
            }

            if (IsLayoutReady)
            {
                MainLayout.IsVisible = true;
                _ = MainLayout.FadeOut(ANIMATE_SPEED_SECS, Easing.Linear, cancellation)
                    .ContinueWith(t =>
                    {
                        if (!cancellation.IsCancellationRequested)
                        {
                            MainLayout.IsVisible = false;
                        }
                    });
            }
            else
            {
                MainLayout.IsVisible = false;
            }
        }

        public void ShowControls()
        {
            var oldCts = Interlocked.Exchange(ref this.cancellation, new CancellationTokenSource());
            if (oldCts != null && !oldCts.IsCancellationRequested)
            {
                oldCts.Cancel();
                oldCts.Dispose();
            }

            if (IsLayoutReady)
            {
                MainLayout.Opacity = 0.001;
                MainLayout.IsVisible = true;
                _ = MainLayout.FadeIn(ANIMATE_SPEED_SECS, Easing.Linear, cancellation)
                    .ContinueWith(t =>
                    {
                        if (!cancellation.IsCancellationRequested)
                        {
                            _inactivityTimer.Kick();
                        }
                    });
            }
            else
            {
                MainLayout.Opacity = 1;
                MainLayout.IsVisible = true;
            }
        }

        public override ISkiaGestureListener ProcessGestures(SkiaGesturesParameters args,
            GestureEventProcessingInfo apply)
        {
            ReportWasTouched();

            return base.ProcessGestures(args, apply);
        }

        public void ReportWasTouched()
        {
            if (MainLayout.IsVisible)
            {
                Debug.WriteLine("Was touched");
                _inactivityTimer.Kick();
            }
        }

        protected override void OnLayoutReady()
        {
            base.OnLayoutReady();

            _inactivityTimer.Kick();
        }
    }
}
