﻿<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <MSBuildAllProjects Condition="'$(MSBuildVersion)' == '' Or '$(MSBuildVersion)' &lt; '16.0'">$(MSBuildAllProjects);$(MSBuildThisFileFullPath)</MSBuildAllProjects>
    <HasSharedItems>true</HasSharedItems>
    <SharedGUID>*************-48dd-bdb3-98edecbb1107</SharedGUID>
  </PropertyGroup>
  <PropertyGroup Label="Configuration">
    <Import_RootNamespace>Shared</Import_RootNamespace>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Base\ObservableAttachedItemsCollection.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Base\VisualLayer.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Base\SkiaControl.Cache.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Base\SkiaControl.Effects.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Base\SkiaControl.Invalidation.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Base\SkiaControl.Shared.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Base\VisualTreeHandler.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Cache\SkiaCacheType.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Cache\SkiaRenderObject.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\AutoSizeType.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\DirectionType.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\DoubleViewTransitionType.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\DrawImageAlignment.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\DrawTextAlignment.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\FontWeight.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\GradientType.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\GridLengthType.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\HardwareAccelerationMode.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\LayoutType.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\LockTouch.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\MeasuringStrategy.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\ObjectAliveType.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\OrientationType.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\PanningModeType.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\PointedDirectionType.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\RangeZone.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\RecycleTemplateType.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\RecyclingTemplate.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\RelativePositionType.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\RescalingType.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\ScrollInteractionState.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\ShapeType.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\SkiaAnchorBak.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\SkiaImageEffect.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\SkiaImageEffects.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\SkiaTouchAnimation.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\SnapToChildrenType.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\SourceType.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\SpaceDistribution.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\TextTransform.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\TransformAspect.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\UpdateMode.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\ViewportScrollType.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Enums\VirtualizationType.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Extensions\FluentExtensions.Shared.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Extensions\InternalExtensions.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Extensions\PointExtensions.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Helpers\IntersectionUtils.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Helpers\Looper.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Helpers\Pendulum.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Helpers\RestartingTimer.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Helpers\RubberBandUtils.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Helpers\Screen.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Helpers\VelocityTracker.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Interfaces\IAnimatorsManager.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Interfaces\IBindingContextDebuggable.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Interfaces\ICanBeUpdated.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Interfaces\IDefinesViewport.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Interfaces\IDrawnBase.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Interfaces\IHasAfterEffects.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Interfaces\IInsideViewport.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Interfaces\IInsideWheelStack.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Interfaces\ILayoutInsideViewport.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Interfaces\IRefreshIndicator.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Interfaces\ISkiaCell.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Interfaces\ISkiaControl.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Interfaces\ISkiaDrawable.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Interfaces\ISkiaGestureListener.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Interfaces\ISkiaGestureProcessor.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Interfaces\ISkiaGridLayout.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Interfaces\ISkiaLayer.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Interfaces\ISkiaLayout.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Interfaces\ISkiaSharpView.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Interfaces\IVisibilityAware.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Interfaces\IWithContent.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\CachedGradient.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\CachedShader.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\CachedShadow.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\ClosedRange.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\ContainsPointResult.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\ContentLoadedEventArgs.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\ControlInStack.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\ControlsTracker.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\DefinitionInfo.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\DescendingZIndexGestureListenerComparer.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\DrawingContext.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\DrawnUiStartupSettings.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\DynamicGrid.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\GridSpan.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\LimitedConcurrentQueue.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\LimitedQueue.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\LimitedStack.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\MeasureRequest.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\MeasuringConstraints.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\OrderedIndex.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\PointIsInsideResult.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\Rectangle.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\RenderOnTimer.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\ScaledPoint.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\ScaledRect.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\ScaledSize.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\SelectableAction.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\SkiaTouchResultContext.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\SortedGestureListeners.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\SpanKey.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\Spring.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\TitleWithStringId.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\Vector.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\VisualTransform.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\VisualTransformNative.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\VisualTreeChain.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\Internals\Models\WindowParameters.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\SkiaBevel.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\SkiaGradient.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\SkiaPoint.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Draw\SkiaShadow.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animations\ExecuteOnTick.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animations\Extensions\AnimateExtensions.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animations\Extensions\SpringExtensions.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animations\HoverEffects\RippleAnimator.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animations\HoverEffects\ShimmerAnimator.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animations\Interfaces\IAfterEffect.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animations\Interfaces\ICanRenderOnCanvas.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animations\Interfaces\IDampingTimingParameters.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animations\Interfaces\IDampingTimingVectorParameters.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animations\Interfaces\ISkiaAnimator.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animations\Parameters\CriticallyDampedSpringTimingVectorParameters.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animations\Parameters\DecelerationTimingParameters.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animations\Parameters\DecelerationTimingVectorParameters.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animations\Parameters\ITimingVectorParameters.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animations\Parameters\SpringTimingParameters.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animations\Parameters\SpringTimingVectorParameters.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animations\Parameters\TimingParameters.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animations\Parameters\UnderdampedSpringTimingVectorParameters.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animations\FrameTimeInterpolator.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animations\ToDo\IInterpolator.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animations\ToDo\ViscousFluidInterpolator.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animators\AnimatorBase.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animators\BlinkAnimator.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animators\ColorBlendAnimator.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animators\EdgeGlowAnimator.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animators\PendulumAnimator.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animators\PerpetualPendulumAnimator.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animators\PingPongAnimator.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animators\ProgressAnimator.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animators\RangeAnimator.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animators\RangeVectorAnimator.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animators\RenderingAnimator.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animators\ScrollFlingAnimator.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animators\ScrollFlingVectorAnimator.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animators\SkiaValueAnimator.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animators\SkiaVectorAnimator.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animators\SpringWithVelocityAnimator.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animators\SpringWithVelocityVectorAnimator.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Animators\VelocitySkiaAnimator.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Fluent\SkiaFrame.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Fluent\SkiaLayer.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Fluent\HStack.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Fluent\SkiaGrid.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Fluent\VStack.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Features\Fluent\SkiaStack.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Internals\Helpers\3D\Sk3dView.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Internals\Helpers\3D\SkCamera3D.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Internals\Helpers\3D\SkPatch3D.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Internals\Helpers\TrackedObject.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Super.cs" />
  </ItemGroup>
</Project>